// Firebase AI Logic service for generating video recommendations

import { getAI, getGenerativeModel, GoogleAIBackend } from 'firebase/ai'
import { getFirebaseApp } from '@/lib/firebase/config'
import { VideoSearchResult } from '@/lib/types/video'
import { youtubeService } from './youtube'

export interface MagicQueueRequest {
  prompt: string
  count?: number
  maxDuration?: number // in minutes, optional filter
  onProgress?: (step: string, progress: number, total: number) => void
}

export interface MagicQueueResponse {
  videos: VideoSearchResult[]
  explanation?: string
  searchQueries?: string[] // The queries that were used
}

export class AIService {
  private getAI() {
    const app = getFirebaseApp()
    if (!app) {
      throw new Error('Firebase not initialized. AI service will not work.')
    }
    
    try {
      return getAI(app, { backend: new GoogleAIBackend() })
    } catch (error) {
      console.error('❌ Error initializing Firebase AI:', error)
      throw new Error('Firebase AI not configured properly. Please check your Firebase AI Logic setup.')
    }
  }

  private getModel() {
    const ai = this.getAI()
    return getGenerativeModel(ai, { model: 'gemini-2.5-flash' })
  }

  /**
   * Generate search queries based on user prompt
   */
  private async generateSearchQueries(prompt: string, count: number): Promise<string[]> {
    const model = this.getModel()

    // OPTIMIZATION: Reduce number of search queries to save quota
    // Changed from count/2 to Math.min(3, Math.ceil(count/3)) to limit API calls
    const queryCount = Math.min(3, Math.ceil(count / 3))

    const systemPrompt = `You are a YouTube search expert. Based on the user's request, generate ${queryCount} different search queries that would find the best videos for their needs.

IMPORTANT: You must respond with a valid JSON array of search queries:
["search query 1", "search query 2", "search query 3"]

Guidelines:
- Create diverse search queries that cover different aspects of the request
- Use terms that are commonly used on YouTube
- Include both broad and specific queries
- Focus on findable, popular content
- Avoid overly complex or niche terms
- Prioritize quality over quantity - fewer, better queries

User request: "${prompt}"`

    const result = await model.generateContent(systemPrompt)
    const responseText = result.response.text()

    try {
      // Extract JSON from response if it's wrapped in markdown code blocks
      const jsonMatch = responseText.match(/```(?:json)?\s*([\s\S]*?)\s*```/)
      const jsonText = jsonMatch ? jsonMatch[1] : responseText
      const queries = JSON.parse(jsonText)

      if (!Array.isArray(queries)) {
        throw new Error('AI response is not an array')
      }

      return queries.filter(q => typeof q === 'string' && q.trim().length > 0)
    } catch (parseError) {
      console.error('❌ Failed to parse search queries:', parseError)
      // Fallback: create basic search queries from the prompt
      return [prompt, `${prompt} tutorial`, `${prompt} guide`]
    }
  }

  /**
   * Select best videos from search results using AI
   */
  private async selectBestVideos(
    prompt: string,
    allVideos: VideoSearchResult[],
    count: number,
    maxDuration?: number
  ): Promise<{ videos: VideoSearchResult[], explanation: string }> {
    const model = this.getModel()

    // Filter by duration if specified
    let filteredVideos = allVideos
    if (maxDuration) {
      filteredVideos = allVideos.filter(video => {
        const duration = this.parseDurationToMinutes(video.duration)
        return duration <= maxDuration
      })
    }

    // If we have fewer videos than requested, just return all
    if (filteredVideos.length <= count) {
      return {
        videos: filteredVideos,
        explanation: `Found ${filteredVideos.length} videos matching your criteria.`
      }
    }

    // OPTIMIZATION: If we have too many videos, pre-filter to top candidates
    // This reduces AI processing time significantly
    if (filteredVideos.length > count * 3) {
      // Sort by view count and take top candidates for AI analysis
      filteredVideos = filteredVideos
        .sort((a, b) => (b.viewCount || 0) - (a.viewCount || 0))
        .slice(0, count * 3) // Analyze only 3x the requested count
    }

    // OPTIMIZATION: Prepare minimal video data for faster AI processing
    const videoData = filteredVideos.map((video, index) => ({
      index,
      title: video.title,
      channel: video.channel.title,
      description: video.description.substring(0, 100), // Reduced from 200 to 100
      duration: video.duration,
      views: this.formatViewCount(video.viewCount || 0)
    }))

    // OPTIMIZATION: Simplified, more focused prompt for faster processing
    const systemPrompt = `Select ${count} best videos for: "${prompt}"

Videos:
${JSON.stringify(videoData, null, 1)}

IMPORTANT: Respond with ONLY valid JSON, no other text:
{
  "selectedIndices": [0, 1, 2],
  "explanation": "Brief reason"
}

Select exactly ${count} video indices (0-${filteredVideos.length - 1}). Prioritize relevance and quality.`

    // Add timeout to prevent hanging
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('AI selection timeout')), 15000) // 15 second timeout
    })

    const result = await Promise.race([
      model.generateContent(systemPrompt),
      timeoutPromise
    ]) as any
    const responseText = result.response.text()

    try {
      console.log('🤖 AI Response:', responseText.substring(0, 200) + '...')

      // Try multiple JSON extraction methods
      let jsonText = ''

      // Method 1: Look for JSON in code blocks
      const codeBlockMatch = responseText.match(/```(?:json)?\s*([\s\S]*?)\s*```/)
      if (codeBlockMatch) {
        jsonText = codeBlockMatch[1].trim()
      } else {
        // Method 2: Look for JSON object pattern
        const jsonMatch = responseText.match(/\{[\s\S]*\}/)
        if (jsonMatch) {
          jsonText = jsonMatch[0]
        } else {
          // Method 3: Use entire response
          jsonText = responseText.trim()
        }
      }

      // Clean up common JSON issues
      jsonText = jsonText
        .replace(/,\s*}/g, '}') // Remove trailing commas
        .replace(/,\s*]/g, ']') // Remove trailing commas in arrays
        .replace(/'/g, '"') // Replace single quotes with double quotes

      console.log('🔧 Cleaned JSON:', jsonText.substring(0, 200) + '...')

      const selection = JSON.parse(jsonText)

      if (!selection.selectedIndices || !Array.isArray(selection.selectedIndices)) {
        throw new Error('Invalid selection format - missing selectedIndices array')
      }

      // Get selected videos
      const selectedVideos = selection.selectedIndices
        .filter((index: number) => typeof index === 'number' && index >= 0 && index < filteredVideos.length)
        .slice(0, count) // Ensure we don't exceed requested count
        .map((index: number) => filteredVideos[index])

      if (selectedVideos.length === 0) {
        throw new Error('No valid video indices selected')
      }

      return {
        videos: selectedVideos,
        explanation: selection.explanation || `Selected ${selectedVideos.length} best videos for your request.`
      }
    } catch (parseError) {
      console.error('❌ Failed to parse video selection:', parseError)
      console.error('❌ Raw AI response:', responseText)

      // Smart fallback: score videos by relevance + popularity
      const fallbackVideos = filteredVideos
        .map(video => ({
          video,
          score: this.calculateVideoScore(video, prompt)
        }))
        .sort((a, b) => b.score - a.score)
        .slice(0, count)
        .map(item => item.video)

      return {
        videos: fallbackVideos,
        explanation: `AI selection failed, showing ${count} best-matching videos by relevance and popularity.`
      }
    }
  }

  /**
   * Parse YouTube duration to minutes
   */
  private parseDurationToMinutes(duration: string): number {
    // Parse ISO 8601 duration format (PT1H2M3S)
    const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/)
    if (!match) return 0

    const hours = parseInt(match[1] || '0')
    const minutes = parseInt(match[2] || '0')
    const seconds = parseInt(match[3] || '0')

    return hours * 60 + minutes + seconds / 60
  }

  /**
   * Format view count for AI processing (simplified)
   */
  private formatViewCount(viewCount: number): string {
    if (viewCount >= 1000000) {
      return `${Math.round(viewCount / 1000000)}M`
    } else if (viewCount >= 1000) {
      return `${Math.round(viewCount / 1000)}K`
    }
    return viewCount.toString()
  }

  /**
   * Calculate video relevance score for fallback selection
   */
  private calculateVideoScore(video: VideoSearchResult, prompt: string): number {
    const promptLower = prompt.toLowerCase()
    const titleLower = video.title.toLowerCase()
    const descLower = video.description.toLowerCase()

    let score = 0

    // Title relevance (highest weight)
    const titleWords = promptLower.split(' ').filter(word => word.length > 2)
    titleWords.forEach(word => {
      if (titleLower.includes(word)) score += 10
    })

    // Description relevance
    titleWords.forEach(word => {
      if (descLower.includes(word)) score += 3
    })

    // View count bonus (normalized)
    const viewScore = Math.log10((video.viewCount || 1) + 1)
    score += viewScore

    return score
  }

  /**
   * Generate video recommendations based on user prompt
   */
  async generateVideoRecommendations(request: MagicQueueRequest): Promise<MagicQueueResponse> {
    const { prompt, count = 10, maxDuration, onProgress } = request

    try {
      console.log('🪄 Generating magic queue for prompt:', prompt)
      console.log('📊 Parameters:', { count, maxDuration })

      // Step 1: Generate search queries using AI
      console.log('🔍 Step 1: Generating search queries...')
      onProgress?.('Generating search queries...', 0, 3)
      const searchQueries = await this.generateSearchQueries(prompt, count)
      console.log('✅ Generated search queries:', searchQueries)
      onProgress?.('Generated search queries', 1, 3)

      // Step 2: Search YouTube for each query (in parallel for speed)
      console.log('🔍 Step 2: Searching YouTube...')
      onProgress?.('Searching YouTube...', 1, 3)

      const searchPromises = searchQueries.map(async (query, index) => {
        try {
          console.log(`  Searching for: "${query}"`)
          // OPTIMIZATION: Reduce search results multiplier to save quota
          // Changed from 1.5x to 1.2x to reduce API calls while still having selection options
          const results = await youtubeService.searchVideos(query, Math.ceil(count * 1.2))
          onProgress?.(`Searched ${index + 1}/${searchQueries.length} queries`, 1 + (index + 1) / searchQueries.length * 0.8, 3)
          return results
        } catch (error) {
          console.warn(`Failed to search for "${query}":`, error)

          // Check if it's a 403 error (API quota/key issue)
          if (error instanceof Error && error.message.includes('403')) {
            console.error('🚨 YouTube API 403 Error - Possible causes:')
            console.error('   • API quota exceeded (daily limit reached)')
            console.error('   • Invalid API key or key restrictions')
            console.error('   • YouTube Data API v3 not enabled')
            console.error('   • Check your Google Cloud Console settings')
          }

          return []
        }
      })

      const searchResults = await Promise.all(searchPromises)
      const allVideos = searchResults.flat()

      // Remove duplicates by video ID
      const uniqueVideos = allVideos.filter((video, index, self) =>
        self.findIndex(v => v.id === video.id) === index
      )

      console.log(`✅ Found ${uniqueVideos.length} unique videos from ${allVideos.length} total results`)
      onProgress?.('Found videos, selecting best ones...', 2, 3)

      if (uniqueVideos.length === 0) {
        throw new Error('No videos found for your request. Try a different prompt.')
      }

      // Step 3: Use AI to select the best videos
      console.log('🤖 Step 3: AI selecting best videos...')
      onProgress?.('AI analyzing videos...', 2.5, 3)
      const selection = await this.selectBestVideos(prompt, uniqueVideos, count, maxDuration)

      console.log(`✅ Selected ${selection.videos.length} best videos`)
      onProgress?.('Complete!', 3, 3)

      return {
        videos: selection.videos,
        explanation: selection.explanation,
        searchQueries
      }

    } catch (error) {
      console.error('❌ Error generating video recommendations:', error)

      if (error instanceof Error) {
        throw error
      }

      throw new Error('Failed to generate video recommendations. Please try again.')
    }
  }

  /**
   * Check if Firebase AI Logic is properly configured
   */
  async checkAIConfiguration(): Promise<boolean> {
    try {
      const model = this.getModel()
      // Try a simple test request
      await model.generateContent('Test')
      return true
    } catch (error) {
      console.error('❌ AI configuration check failed:', error)
      return false
    }
  }
}

// Export singleton instance
export const aiService = new AIService()
