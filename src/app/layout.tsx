import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { AppProviders } from '@/components/providers/AppProviders'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Tubli - YouTube Looper',
  description: 'A powerful YouTube video queue and looper tool with real-time search functionality using YouTube Data API v3, Firebase Firestore for cloud storage, and persistent queue sharing that allows you to find, queue, and loop multiple videos infinitely.',
  keywords: ['YouTube', 'video', 'queue', 'looper', 'playlist', 'music', 'streaming'],
  authors: [{ name: 'HDCode', url: 'https://hdcode.dev' }],
  openGraph: {
    title: 'Tubli - YouTube Looper',
    description: 'Create and loop YouTube video queues with real-time search and cloud synchronization',
    type: 'website',
    url: 'https://tubli.to',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Tubli - YouTube Looper',
    description: 'Create and loop YouTube video queues with real-time search and cloud synchronization',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="dark">
      <head>
        <link rel="icon" href="/favicon.ico" />
        <meta name="cache-control" content="no-cache, no-store, must-revalidate" />
        <meta name="pragma" content="no-cache" />
        <meta name="expires" content="0" />
      </head>
      <body className={`${inter.className} bg-dark-900 text-white min-h-screen`}>
        <AppProviders>
          {children}
        </AppProviders>
      </body>
    </html>
  )
}
